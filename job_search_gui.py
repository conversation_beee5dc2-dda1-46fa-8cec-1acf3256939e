#!/usr/bin/env python3
"""
Austrian Job Search GUI
Simple graphical interface for the job search automation
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog, scrolledtext
import json
import threading
import subprocess
import sys
from datetime import datetime
import os

class JobSearchGUI:
    """Simple GUI for job search automation"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("Austrian Job Search Automation")
        self.root.geometry("800x600")
        
        # Load configuration
        self.config = self.load_config()
        
        self.create_widgets()
        self.populate_fields()
    
    def load_config(self):
        """Load configuration from file"""
        try:
            with open("config.json", 'r', encoding='utf-8') as f:
                return json.load(f)
        except:
            return self.get_default_config()
    
    def get_default_config(self):
        """Get default configuration"""
        return {
            "personal_info": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"},
            "job_search_criteria": {
                "keywords": ["software engineer", "developer", "python"],
                "locations": ["Wien", "Graz", "Salzburg"],
                "experience_level": "mid"
            }
        }
    
    def save_config(self):
        """Save configuration to file"""
        try:
            with open("config.json", 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
            return True
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save configuration: {e}")
            return False
    
    def create_widgets(self):
        """Create GUI widgets"""
        
        # Create notebook for tabs
        notebook = ttk.Notebook(self.root)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Personal Info Tab
        personal_frame = ttk.Frame(notebook)
        notebook.add(personal_frame, text="Personal Info")
        self.create_personal_tab(personal_frame)
        
        # Search Criteria Tab
        criteria_frame = ttk.Frame(notebook)
        notebook.add(criteria_frame, text="Search Criteria")
        self.create_criteria_tab(criteria_frame)
        
        # Results Tab
        results_frame = ttk.Frame(notebook)
        notebook.add(results_frame, text="Results")
        self.create_results_tab(results_frame)
        
        # Control buttons
        button_frame = ttk.Frame(self.root)
        button_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Button(button_frame, text="Save Configuration", 
                  command=self.save_configuration).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Run Job Search", 
                  command=self.run_job_search).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="View Results", 
                  command=self.view_results).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Exit", 
                  command=self.root.quit).pack(side=tk.RIGHT, padx=5)
    
    def create_personal_tab(self, parent):
        """Create personal information tab"""
        
        # Name
        ttk.Label(parent, text="Name:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.name_var = tk.StringVar()
        ttk.Entry(parent, textvariable=self.name_var, width=40).grid(row=0, column=1, padx=5, pady=5)
        
        # Email
        ttk.Label(parent, text="Email:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.email_var = tk.StringVar()
        ttk.Entry(parent, textvariable=self.email_var, width=40).grid(row=1, column=1, padx=5, pady=5)
        
        # Experience Level
        ttk.Label(parent, text="Experience Level:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        self.experience_var = tk.StringVar()
        experience_combo = ttk.Combobox(parent, textvariable=self.experience_var, 
                                       values=["junior", "mid", "senior", "executive"])
        experience_combo.grid(row=2, column=1, padx=5, pady=5, sticky=tk.W)
    
    def create_criteria_tab(self, parent):
        """Create search criteria tab"""
        
        # Keywords
        ttk.Label(parent, text="Keywords (comma-separated):").grid(row=0, column=0, sticky=tk.NW, padx=5, pady=5)
        self.keywords_text = scrolledtext.ScrolledText(parent, height=4, width=50)
        self.keywords_text.grid(row=0, column=1, padx=5, pady=5)
        
        # Job Titles
        ttk.Label(parent, text="Job Titles (comma-separated):").grid(row=1, column=0, sticky=tk.NW, padx=5, pady=5)
        self.job_titles_text = scrolledtext.ScrolledText(parent, height=4, width=50)
        self.job_titles_text.grid(row=1, column=1, padx=5, pady=5)
        
        # Locations
        ttk.Label(parent, text="Locations (comma-separated):").grid(row=2, column=0, sticky=tk.NW, padx=5, pady=5)
        self.locations_text = scrolledtext.ScrolledText(parent, height=3, width=50)
        self.locations_text.grid(row=2, column=1, padx=5, pady=5)
        
        # Min Match Score
        ttk.Label(parent, text="Minimum Match Score (%):").grid(row=3, column=0, sticky=tk.W, padx=5, pady=5)
        self.min_score_var = tk.StringVar(value="40")
        ttk.Entry(parent, textvariable=self.min_score_var, width=10).grid(row=3, column=1, padx=5, pady=5, sticky=tk.W)
    
    def create_results_tab(self, parent):
        """Create results tab"""
        
        # Results text area
        ttk.Label(parent, text="Search Results:").pack(anchor=tk.W, padx=5, pady=5)
        self.results_text = scrolledtext.ScrolledText(parent, height=20, width=80)
        self.results_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Progress bar
        self.progress_var = tk.StringVar(value="Ready")
        ttk.Label(parent, textvariable=self.progress_var).pack(pady=5)
        
        self.progress_bar = ttk.Progressbar(parent, mode='indeterminate')
        self.progress_bar.pack(fill=tk.X, padx=5, pady=5)
    
    def populate_fields(self):
        """Populate fields with configuration data"""
        
        # Personal info
        personal = self.config.get("personal_info", {})
        self.name_var.set(personal.get("name", ""))
        self.email_var.set(personal.get("email", ""))
        
        # Search criteria
        criteria = self.config.get("job_search_criteria", {})
        self.experience_var.set(criteria.get("experience_level", "mid"))
        
        # Keywords
        keywords = criteria.get("keywords", [])
        self.keywords_text.insert(tk.END, ", ".join(keywords))
        
        # Job titles
        job_titles = criteria.get("job_titles", [])
        self.job_titles_text.insert(tk.END, ", ".join(job_titles))
        
        # Locations
        locations = criteria.get("locations", [])
        self.locations_text.insert(tk.END, ", ".join(locations))
    
    def save_configuration(self):
        """Save current configuration"""
        
        # Update config with current values
        self.config["personal_info"] = {
            "name": self.name_var.get(),
            "email": self.email_var.get()
        }
        
        keywords = [k.strip() for k in self.keywords_text.get(1.0, tk.END).strip().split(",") if k.strip()]
        job_titles = [j.strip() for j in self.job_titles_text.get(1.0, tk.END).strip().split(",") if j.strip()]
        locations = [l.strip() for l in self.locations_text.get(1.0, tk.END).strip().split(",") if l.strip()]
        
        self.config["job_search_criteria"] = {
            "keywords": keywords,
            "job_titles": job_titles,
            "locations": locations,
            "experience_level": self.experience_var.get(),
            "min_match_score": float(self.min_score_var.get() or 40)
        }
        
        if self.save_config():
            messagebox.showinfo("Success", "Configuration saved successfully!")
    
    def run_job_search(self):
        """Run job search in background thread"""
        
        # Save configuration first
        self.save_configuration()
        
        # Start progress bar
        self.progress_bar.start()
        self.progress_var.set("Running job search...")
        
        # Clear results
        self.results_text.delete(1.0, tk.END)
        
        # Run in background thread
        thread = threading.Thread(target=self._run_search_thread)
        thread.daemon = True
        thread.start()
    
    def _run_search_thread(self):
        """Background thread for job search"""
        try:
            # Run the job search script
            result = subprocess.run([
                sys.executable, 'austrian_job_search.py'
            ], capture_output=True, text=True, timeout=1800)
            
            # Update UI in main thread
            self.root.after(0, self._search_completed, result)
            
        except Exception as e:
            self.root.after(0, self._search_error, str(e))
    
    def _search_completed(self, result):
        """Handle search completion"""
        self.progress_bar.stop()
        
        if result.returncode == 0:
            self.progress_var.set("Search completed successfully!")
            self.results_text.insert(tk.END, result.stdout)
        else:
            self.progress_var.set("Search failed!")
            self.results_text.insert(tk.END, f"Error: {result.stderr}")
        
        # Auto-scroll to bottom
        self.results_text.see(tk.END)
    
    def _search_error(self, error):
        """Handle search error"""
        self.progress_bar.stop()
        self.progress_var.set("Search failed!")
        self.results_text.insert(tk.END, f"Error: {error}")
        messagebox.showerror("Error", f"Job search failed: {error}")
    
    def view_results(self):
        """Open results folder"""
        try:
            if sys.platform == "win32":
                os.startfile(".")
            elif sys.platform == "darwin":
                subprocess.run(["open", "."])
            else:
                subprocess.run(["xdg-open", "."])
        except Exception as e:
            messagebox.showerror("Error", f"Could not open results folder: {e}")

def main():
    """Main function"""
    root = tk.Tk()
    app = JobSearchGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
