@echo off
echo Austrian Job Search Automation
echo ==============================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python is not installed or not in PATH
    echo Please install Python 3.7+ from https://python.org
    pause
    exit /b 1
)

REM Check if requirements are installed
echo Checking dependencies...
pip show requests >nul 2>&1
if errorlevel 1 (
    echo Installing required packages...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo Error: Failed to install dependencies
        pause
        exit /b 1
    )
)

echo.
echo Choose an option:
echo 1. Run job search (command line)
echo 2. Open GUI interface
echo 3. Start scheduler
echo 4. Install/Update dependencies
echo 5. Exit
echo.

set /p choice="Enter your choice (1-5): "

if "%choice%"=="1" (
    echo Running job search...
    python austrian_job_search.py
    pause
) else if "%choice%"=="2" (
    echo Starting GUI...
    python job_search_gui.py
) else if "%choice%"=="3" (
    echo Starting scheduler...
    python scheduler.py
    pause
) else if "%choice%"=="4" (
    echo Installing/updating dependencies...
    pip install -r requirements.txt --upgrade
    pause
) else if "%choice%"=="5" (
    echo Goodbye!
    exit /b 0
) else (
    echo Invalid choice. Running default job search...
    python austrian_job_search.py
    pause
)
