{"personal_info": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "phone": "", "linkedin": "", "github": ""}, "job_search_criteria": {"keywords": ["software engineer", "software developer", "programmer", "python developer", "java developer", "web developer", "data analyst", "business analyst", "project manager", "IT consultant", "system administrator", "devops engineer"], "job_titles": ["Software Engineer", "Software Developer", "Senior Developer", "Full Stack Developer", "Backend Developer", "Frontend Developer", "Python Developer", "Java Developer", "Data Analyst", "Business Analyst", "Project Manager", "IT Consultant", "System Administrator", "DevOps Engineer"], "locations": ["Wien", "Vienna", "Graz", "Salzburg", "Innsbruck", "Linz", "Klagenfurt", "Österreich", "Austria", "Remote"], "industries": ["Information Technology", "Software Development", "Financial Services", "Banking", "Insurance", "Consulting", "Healthcare", "Manufacturing", "E-commerce", "Telecommunications", "Automotive", "Energy"], "employment_types": ["Vollzeit", "Full-time", "Teilzeit", "Part-time", "Freelance", "Contract", "Remote", "Hybrid"], "experience_level": "mid", "min_salary": null, "max_age_days": 30, "company_sizes": ["startup", "sme", "large"]}, "technical_skills": {"programming_languages": ["Python", "Java", "JavaScript", "TypeScript", "C#", "SQL", "HTML", "CSS"], "frameworks": ["Django", "Flask", "Spring Boot", "React", "Angular", "Vue.js", "Node.js", ".NET"], "databases": ["PostgreSQL", "MySQL", "MongoDB", "Redis", "Oracle", "SQL Server"], "tools": ["Git", "<PERSON>er", "Kubernetes", "<PERSON>", "AWS", "Azure", "Google Cloud", "<PERSON><PERSON>", "Confluence"]}, "languages": {"German": "B2", "English": "C1", "Arabic": "Native", "French": "A2"}, "education": {"degree": "Bachelor/Master in Computer Science/Engineering", "university": "", "graduation_year": "", "certifications": []}, "experience": {"total_years": "5+", "current_position": "", "previous_positions": [], "key_achievements": []}, "preferences": {"preferred_company_culture": ["innovative", "collaborative", "growth-oriented"], "work_arrangement": ["hybrid", "remote", "office"], "career_goals": ["technical leadership", "project management", "specialization"], "industries_to_avoid": [], "salary_expectations": {"currency": "EUR", "min_annual": null, "max_annual": null}}, "automation_settings": {"search_frequency": "daily", "max_applications_per_day": 5, "auto_apply": false, "email_notifications": true, "min_match_score": 40.0, "save_format": ["excel", "json"], "generate_cover_letters": true, "cover_letter_language": "german"}, "job_boards": {"jobs_at": {"enabled": true, "priority": 1, "rate_limit_seconds": 2}, "karriere_at": {"enabled": true, "priority": 2, "rate_limit_seconds": 2}, "stepstone_at": {"enabled": true, "priority": 3, "rate_limit_seconds": 3}, "xing_jobs": {"enabled": false, "priority": 4, "rate_limit_seconds": 3}, "linkedin_jobs": {"enabled": false, "priority": 5, "rate_limit_seconds": 5}, "willhaben_jobs": {"enabled": true, "priority": 6, "rate_limit_seconds": 2}}, "email_settings": {"smtp_server": "smtp.gmail.com", "smtp_port": 587, "sender_email": "", "sender_password": "", "recipient_email": ""}}