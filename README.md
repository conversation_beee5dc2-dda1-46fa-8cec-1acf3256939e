# Austrian Job Search Automation

Automated job search tool specifically designed for finding employment opportunities in Austria. Searches major Austrian job boards and provides personalized job matching, application tracking, and cover letter generation.

## 🎯 Features

- **Multi-Board Search**: Searches jobs.at, karriere.at, stepstone.at, and other Austrian job boards
- **Intelligent Matching**: Calculates match scores based on your skills and preferences
- **Application Tracking**: Track application status and manage your job search pipeline
- **Cover Letter Generation**: Generate personalized German and English cover letters
- **Email Notifications**: Get alerts for new matching positions
- **Export Options**: Save results to Excel and JSON formats
- **Rate Limiting**: Respectful scraping with built-in delays

## 🚀 Quick Start

### 1. Installation

```bash
# Clone or download the files
# Install Python dependencies
pip install -r requirements.txt
```

### 2. Configuration

Edit `config.json` to customize your search criteria:

```json
{
  "personal_info": {
    "name": "Your Name",
    "email": "<EMAIL>"
  },
  "job_search_criteria": {
    "keywords": ["your", "skills", "here"],
    "locations": ["Wien", "Graz", "Salzburg"],
    "experience_level": "mid"
  }
}
```

### 3. Customize Your Profile

**IMPORTANT**: Update the following in `austrian_job_search.py`:

```python
# In JobSearchCriteria class, update default values:
keywords = ["your", "technical", "skills"]
job_titles = ["Your Target Job Titles"]
locations = ["Your Preferred Cities"]

# In CoverLetterGenerator class, update:
experience_years = "Your Years of Experience"
experience_field = "Your Field"
qualifications = "Your Key Qualifications"
```

### 4. Run the Search

```bash
python austrian_job_search.py
```

## 📋 Customization Guide

### Personal Information
Update these fields based on your CV:

1. **Technical Skills**: Add your programming languages, frameworks, tools
2. **Experience Level**: junior, mid, senior, executive
3. **Preferred Locations**: Austrian cities where you want to work
4. **Industry Preferences**: Target industries
5. **Language Skills**: German level, English level, etc.

### Job Search Criteria
Customize in `config.json`:

```json
{
  "keywords": [
    "python", "java", "javascript", "react", "django",
    "data analysis", "machine learning", "devops"
  ],
  "job_titles": [
    "Software Engineer", "Data Scientist", "DevOps Engineer"
  ],
  "locations": [
    "Wien", "Graz", "Salzburg", "Remote"
  ]
}
```

## 🔧 Advanced Features

### Application Tracking

```python
# Track applications
tracker = ApplicationTracker()
tracker.add_application(job, status="applied")
tracker.update_status("Company", "Job Title", "interview", "Phone interview scheduled")
```

### Cover Letter Generation

```python
# Generate German cover letter
cover_letter_gen = CoverLetterGenerator(criteria)
cover_letter = cover_letter_gen.generate_cover_letter(job, "german")
```

### Email Notifications

```python
# Set up email alerts
notifier = EmailNotifier()
notifier.send_job_alert(jobs, "<EMAIL>", "<EMAIL>", "password")
```

## 📊 Output Files

The script generates several output files:

- `austrian_jobs_YYYYMMDD_HHMMSS.xlsx` - Excel file with all job listings
- `austrian_jobs_YYYYMMDD_HHMMSS.json` - JSON file with detailed job data
- `applications.json` - Application tracking database
- `cover_letter_Company_JobTitle_german.txt` - Generated cover letters
- `job_search.log` - Detailed logging information

## 🎯 Austrian Job Boards Supported

1. **jobs.at** - Austria's largest job portal
2. **karriere.at** - Major Austrian career platform
3. **stepstone.at** - International job board (Austria)
4. **willhaben.at/jobs** - Classified ads including jobs
5. **xing.com/jobs** - Professional network jobs (optional)
6. **linkedin.com/jobs** - LinkedIn jobs (optional)

## 🔒 Privacy & Ethics

- **Rate Limiting**: Built-in delays to respect website resources
- **User-Agent**: Identifies as a regular browser
- **No Personal Data Storage**: Only stores job information you choose to save
- **Respectful Scraping**: Follows robots.txt guidelines where possible

## 🛠️ Troubleshooting

### Common Issues

1. **No jobs found**: 
   - Check your keywords are not too specific
   - Verify location names are correct
   - Lower the minimum match score

2. **Rate limiting errors**:
   - Increase delay times in the script
   - Run searches during off-peak hours

3. **Email notifications not working**:
   - Enable "Less secure app access" for Gmail
   - Use app-specific passwords for Gmail
   - Check SMTP settings

### Debugging

Enable detailed logging:
```python
logging.basicConfig(level=logging.DEBUG)
```

## 📈 Usage Tips

1. **Start Broad**: Begin with general keywords, then narrow down
2. **Regular Updates**: Run daily or weekly for best results
3. **Track Everything**: Use the application tracker to manage your pipeline
4. **Customize Cover Letters**: Personalize the templates for better results
5. **Monitor Match Scores**: Adjust criteria based on score distributions

## 🔄 Automation

Set up scheduled runs using Windows Task Scheduler or cron:

```bash
# Run daily at 9 AM
0 9 * * * /usr/bin/python3 /path/to/austrian_job_search.py
```

## 📞 Support

For issues or questions:
1. Check the log files for error details
2. Verify your configuration settings
3. Test with a smaller set of keywords first
4. Ensure all dependencies are installed correctly

## ⚖️ Legal Notice

This tool is for personal job searching purposes only. Users are responsible for:
- Complying with website terms of service
- Respecting rate limits and server resources
- Using scraped data ethically and legally
- Following Austrian employment and data protection laws

---

**Good luck with your job search in Austria! 🇦🇹**
