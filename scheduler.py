#!/usr/bin/env python3
"""
Job Search Scheduler
Automatically runs job searches at specified intervals
"""

import schedule
import time
import logging
import subprocess
import sys
from datetime import datetime
import json
import os

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('scheduler.log'),
        logging.StreamHandler()
    ]
)

class JobSearchScheduler:
    """Scheduler for automated job searches"""
    
    def __init__(self, config_file: str = "config.json"):
        self.config_file = config_file
        self.config = self.load_config()
        
    def load_config(self):
        """Load configuration from file"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logging.error(f"Error loading config: {e}")
            return {}
    
    def run_job_search(self):
        """Execute the job search script"""
        try:
            logging.info("Starting scheduled job search...")
            
            # Run the main job search script
            result = subprocess.run([
                sys.executable, 'austrian_job_search.py'
            ], capture_output=True, text=True, timeout=1800)  # 30 minute timeout
            
            if result.returncode == 0:
                logging.info("Job search completed successfully")
                logging.info(f"Output: {result.stdout}")
            else:
                logging.error(f"Job search failed with return code {result.returncode}")
                logging.error(f"Error: {result.stderr}")
                
        except subprocess.TimeoutExpired:
            logging.error("Job search timed out after 30 minutes")
        except Exception as e:
            logging.error(f"Error running job search: {e}")
    
    def setup_schedule(self):
        """Set up the search schedule based on configuration"""
        frequency = self.config.get('automation_settings', {}).get('search_frequency', 'daily')
        
        if frequency == 'daily':
            # Run daily at 9 AM
            schedule.every().day.at("09:00").do(self.run_job_search)
            logging.info("Scheduled daily job search at 09:00")
            
        elif frequency == 'twice_daily':
            # Run twice daily at 9 AM and 6 PM
            schedule.every().day.at("09:00").do(self.run_job_search)
            schedule.every().day.at("18:00").do(self.run_job_search)
            logging.info("Scheduled job search twice daily at 09:00 and 18:00")
            
        elif frequency == 'weekly':
            # Run weekly on Monday at 9 AM
            schedule.every().monday.at("09:00").do(self.run_job_search)
            logging.info("Scheduled weekly job search on Monday at 09:00")
            
        elif frequency == 'hourly':
            # Run every hour (for testing)
            schedule.every().hour.do(self.run_job_search)
            logging.info("Scheduled hourly job search")
            
        else:
            # Default to daily
            schedule.every().day.at("09:00").do(self.run_job_search)
            logging.info("Using default daily schedule at 09:00")
    
    def run_scheduler(self):
        """Start the scheduler"""
        self.setup_schedule()
        
        logging.info("Job search scheduler started")
        logging.info("Press Ctrl+C to stop the scheduler")
        
        try:
            while True:
                schedule.run_pending()
                time.sleep(60)  # Check every minute
                
        except KeyboardInterrupt:
            logging.info("Scheduler stopped by user")
        except Exception as e:
            logging.error(f"Scheduler error: {e}")

def main():
    """Main function"""
    print("🕐 Austrian Job Search Scheduler")
    print("=" * 40)
    
    scheduler = JobSearchScheduler()
    
    # Show current schedule
    config = scheduler.config
    frequency = config.get('automation_settings', {}).get('search_frequency', 'daily')
    print(f"Search frequency: {frequency}")
    print(f"Configuration file: {scheduler.config_file}")
    
    # Option to run immediately
    print("\nOptions:")
    print("1. Start scheduler (runs automatically)")
    print("2. Run job search now")
    print("3. Exit")
    
    choice = input("\nEnter your choice (1-3): ").strip()
    
    if choice == "1":
        scheduler.run_scheduler()
    elif choice == "2":
        print("Running job search now...")
        scheduler.run_job_search()
    elif choice == "3":
        print("Goodbye!")
    else:
        print("Invalid choice. Starting scheduler...")
        scheduler.run_scheduler()

if __name__ == "__main__":
    main()
