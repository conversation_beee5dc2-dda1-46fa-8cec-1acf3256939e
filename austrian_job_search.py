#!/usr/bin/env python3
"""
Austrian Job Search Automation Script
Searches major Austrian job boards for relevant positions
Author: Customized for <PERSON><PERSON>
"""

import requests
from bs4 import BeautifulSoup
import pandas as pd
import time
import json
import logging
from datetime import datetime, timedelta
from urllib.parse import urljoin, quote_plus
import re
import os
from dataclasses import dataclass
from typing import List, Dict, Optional
import smtplib
from email.mime.text import MimeText
from email.mime.multipart import MimeMultipart

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('job_search.log'),
        logging.StreamHandler()
    ]
)

@dataclass
class JobSearchCriteria:
    """Job search criteria configuration"""
    # Personal Information (customize these)
    name: str = "<PERSON><PERSON> Mouelhi"
    email: str = "<EMAIL>"  # From PDF metadata
    
    # Job Search Criteria (CUSTOMIZE THESE BASED ON YOUR CV)
    keywords: List[str] = None
    job_titles: List[str] = None
    locations: List[str] = None
    industries: List[str] = None
    experience_level: str = "mid"  # junior, mid, senior, executive
    employment_types: List[str] = None
    languages: List[str] = None
    
    # Filters
    min_salary: Optional[int] = None
    max_age_days: int = 30
    company_sizes: List[str] = None  # startup, sme, large
    
    def __post_init__(self):
        # Default values - CUSTOMIZE THESE
        if self.keywords is None:
            self.keywords = [
                # Add your technical skills here
                "software engineer", "developer", "programmer",
                "python", "java", "javascript", "data analyst",
                "project manager", "business analyst"
            ]
        
        if self.job_titles is None:
            self.job_titles = [
                "Software Engineer", "Software Developer", "Programmer",
                "Data Analyst", "Business Analyst", "Project Manager",
                "IT Consultant", "System Administrator"
            ]
        
        if self.locations is None:
            self.locations = [
                "Wien", "Vienna", "Graz", "Salzburg", "Innsbruck",
                "Linz", "Klagenfurt", "Österreich", "Austria"
            ]
        
        if self.industries is None:
            self.industries = [
                "IT", "Software", "Technology", "Finance", "Banking",
                "Consulting", "Healthcare", "Manufacturing"
            ]
        
        if self.employment_types is None:
            self.employment_types = ["Vollzeit", "Teilzeit", "Freelance", "Remote"]
        
        if self.languages is None:
            self.languages = ["Deutsch", "English", "German"]
        
        if self.company_sizes is None:
            self.company_sizes = ["startup", "sme", "large"]

@dataclass
class JobListing:
    """Job listing data structure"""
    title: str
    company: str
    location: str
    description: str
    url: str
    salary: Optional[str]
    posted_date: Optional[str]
    employment_type: Optional[str]
    source: str
    match_score: float = 0.0
    applied: bool = False
    notes: str = ""

class AustrianJobSearcher:
    """Main job search automation class"""
    
    def __init__(self, criteria: JobSearchCriteria):
        self.criteria = criteria
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        self.jobs_found = []
        
        # Austrian job board URLs
        self.job_boards = {
            'jobs_at': 'https://www.jobs.at',
            'karriere_at': 'https://www.karriere.at',
            'stepstone_at': 'https://www.stepstone.at',
            'xing_jobs': 'https://www.xing.com/jobs',
            'linkedin_jobs': 'https://www.linkedin.com/jobs',
            'willhaben_jobs': 'https://www.willhaben.at/jobs'
        }
    
    def search_jobs_at(self) -> List[JobListing]:
        """Search jobs.at"""
        jobs = []
        logging.info("Searching jobs.at...")
        
        try:
            for keyword in self.criteria.keywords[:3]:  # Limit to avoid rate limiting
                for location in self.criteria.locations[:2]:
                    url = f"https://www.jobs.at/jobs?k={quote_plus(keyword)}&l={quote_plus(location)}"
                    
                    response = self.session.get(url, timeout=10)
                    if response.status_code == 200:
                        soup = BeautifulSoup(response.content, 'html.parser')
                        
                        # Parse job listings (adjust selectors based on actual site structure)
                        job_elements = soup.find_all('div', class_='job-item') or soup.find_all('article')
                        
                        for job_elem in job_elements[:10]:  # Limit results
                            try:
                                title = self._extract_text(job_elem, ['h2', 'h3', '.job-title'])
                                company = self._extract_text(job_elem, ['.company', '.employer'])
                                location_text = self._extract_text(job_elem, ['.location', '.job-location'])
                                
                                if title and company:
                                    job = JobListing(
                                        title=title,
                                        company=company,
                                        location=location_text or location,
                                        description="",
                                        url=self._extract_link(job_elem),
                                        salary=self._extract_text(job_elem, ['.salary', '.wage']),
                                        posted_date=self._extract_text(job_elem, ['.date', '.posted']),
                                        employment_type=None,
                                        source="jobs.at"
                                    )
                                    jobs.append(job)
                            except Exception as e:
                                logging.warning(f"Error parsing job element: {e}")
                                continue
                    
                    time.sleep(2)  # Rate limiting
        
        except Exception as e:
            logging.error(f"Error searching jobs.at: {e}")
        
        logging.info(f"Found {len(jobs)} jobs on jobs.at")
        return jobs
    
    def search_karriere_at(self) -> List[JobListing]:
        """Search karriere.at"""
        jobs = []
        logging.info("Searching karriere.at...")
        
        try:
            for keyword in self.criteria.keywords[:3]:
                url = f"https://www.karriere.at/jobs?keywords={quote_plus(keyword)}"
                
                response = self.session.get(url, timeout=10)
                if response.status_code == 200:
                    soup = BeautifulSoup(response.content, 'html.parser')
                    
                    job_elements = soup.find_all('div', class_='job') or soup.find_all('article')
                    
                    for job_elem in job_elements[:10]:
                        try:
                            title = self._extract_text(job_elem, ['h2', 'h3', '.job-title'])
                            company = self._extract_text(job_elem, ['.company', '.employer'])
                            location_text = self._extract_text(job_elem, ['.location'])
                            
                            if title and company:
                                job = JobListing(
                                    title=title,
                                    company=company,
                                    location=location_text or "Austria",
                                    description="",
                                    url=self._extract_link(job_elem),
                                    salary=None,
                                    posted_date=None,
                                    employment_type=None,
                                    source="karriere.at"
                                )
                                jobs.append(job)
                        except Exception as e:
                            logging.warning(f"Error parsing job element: {e}")
                            continue
                
                time.sleep(2)
        
        except Exception as e:
            logging.error(f"Error searching karriere.at: {e}")
        
        logging.info(f"Found {len(jobs)} jobs on karriere.at")
        return jobs
    
    def _extract_text(self, element, selectors: List[str]) -> Optional[str]:
        """Extract text from element using multiple selectors"""
        for selector in selectors:
            try:
                found = element.select_one(selector) or element.find(class_=selector.replace('.', ''))
                if found:
                    return found.get_text(strip=True)
            except:
                continue
        return None
    
    def _extract_link(self, element) -> str:
        """Extract job link from element"""
        try:
            link_elem = element.find('a', href=True)
            if link_elem:
                href = link_elem['href']
                if href.startswith('http'):
                    return href
                else:
                    return urljoin(self.session.headers.get('Referer', ''), href)
        except:
            pass
        return ""
    
    def calculate_match_score(self, job: JobListing) -> float:
        """Calculate how well a job matches the criteria"""
        score = 0.0
        text_to_check = f"{job.title} {job.description} {job.company}".lower()
        
        # Keyword matching
        keyword_matches = sum(1 for keyword in self.criteria.keywords 
                            if keyword.lower() in text_to_check)
        score += (keyword_matches / len(self.criteria.keywords)) * 40
        
        # Location matching
        location_matches = any(loc.lower() in job.location.lower() 
                             for loc in self.criteria.locations)
        if location_matches:
            score += 20
        
        # Job title matching
        title_matches = any(title.lower() in job.title.lower() 
                          for title in self.criteria.job_titles)
        if title_matches:
            score += 30
        
        # Company and other factors
        if job.company:
            score += 10
        
        return min(score, 100.0)

    def search_all_boards(self) -> List[JobListing]:
        """Search all configured job boards"""
        all_jobs = []

        # Search each job board
        all_jobs.extend(self.search_jobs_at())
        all_jobs.extend(self.search_karriere_at())

        # Calculate match scores
        for job in all_jobs:
            job.match_score = self.calculate_match_score(job)

        # Remove duplicates based on title and company
        unique_jobs = []
        seen = set()
        for job in all_jobs:
            key = (job.title.lower(), job.company.lower())
            if key not in seen:
                seen.add(key)
                unique_jobs.append(job)

        # Sort by match score
        unique_jobs.sort(key=lambda x: x.match_score, reverse=True)

        self.jobs_found = unique_jobs
        logging.info(f"Total unique jobs found: {len(unique_jobs)}")
        return unique_jobs

    def filter_jobs(self, min_score: float = 30.0) -> List[JobListing]:
        """Filter jobs based on minimum match score"""
        filtered = [job for job in self.jobs_found if job.match_score >= min_score]
        logging.info(f"Jobs after filtering (min score {min_score}): {len(filtered)}")
        return filtered

    def save_to_excel(self, filename: str = None):
        """Save job listings to Excel file"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"austrian_jobs_{timestamp}.xlsx"

        if not self.jobs_found:
            logging.warning("No jobs to save")
            return

        # Convert to DataFrame
        data = []
        for job in self.jobs_found:
            data.append({
                'Title': job.title,
                'Company': job.company,
                'Location': job.location,
                'Salary': job.salary,
                'Employment Type': job.employment_type,
                'Posted Date': job.posted_date,
                'Match Score': f"{job.match_score:.1f}%",
                'Source': job.source,
                'URL': job.url,
                'Applied': job.applied,
                'Notes': job.notes
            })

        df = pd.DataFrame(data)
        df.to_excel(filename, index=False)
        logging.info(f"Jobs saved to {filename}")
        return filename

    def save_to_json(self, filename: str = None):
        """Save job listings to JSON file"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"austrian_jobs_{timestamp}.json"

        data = []
        for job in self.jobs_found:
            data.append({
                'title': job.title,
                'company': job.company,
                'location': job.location,
                'description': job.description,
                'url': job.url,
                'salary': job.salary,
                'posted_date': job.posted_date,
                'employment_type': job.employment_type,
                'source': job.source,
                'match_score': job.match_score,
                'applied': job.applied,
                'notes': job.notes
            })

        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)

        logging.info(f"Jobs saved to {filename}")
        return filename

class ApplicationTracker:
    """Track job applications and their status"""

    def __init__(self, filename: str = "applications.json"):
        self.filename = filename
        self.applications = self.load_applications()

    def load_applications(self) -> Dict:
        """Load existing applications from file"""
        try:
            if os.path.exists(self.filename):
                with open(self.filename, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            logging.error(f"Error loading applications: {e}")
        return {}

    def save_applications(self):
        """Save applications to file"""
        try:
            with open(self.filename, 'w', encoding='utf-8') as f:
                json.dump(self.applications, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logging.error(f"Error saving applications: {e}")

    def add_application(self, job: JobListing, status: str = "applied"):
        """Add a new application"""
        app_id = f"{job.company}_{job.title}".replace(" ", "_").lower()
        self.applications[app_id] = {
            'job_title': job.title,
            'company': job.company,
            'location': job.location,
            'url': job.url,
            'applied_date': datetime.now().isoformat(),
            'status': status,  # applied, interview, rejected, offer, accepted
            'notes': job.notes,
            'source': job.source
        }
        self.save_applications()
        logging.info(f"Added application: {job.title} at {job.company}")

    def update_status(self, company: str, title: str, status: str, notes: str = ""):
        """Update application status"""
        app_id = f"{company}_{title}".replace(" ", "_").lower()
        if app_id in self.applications:
            self.applications[app_id]['status'] = status
            self.applications[app_id]['last_updated'] = datetime.now().isoformat()
            if notes:
                self.applications[app_id]['notes'] = notes
            self.save_applications()
            logging.info(f"Updated application status: {title} at {company} -> {status}")
        else:
            logging.warning(f"Application not found: {title} at {company}")

    def get_applications_summary(self) -> Dict:
        """Get summary of applications by status"""
        summary = {}
        for app in self.applications.values():
            status = app['status']
            summary[status] = summary.get(status, 0) + 1
        return summary

class CoverLetterGenerator:
    """Generate personalized cover letters"""

    def __init__(self, criteria: JobSearchCriteria):
        self.criteria = criteria

        # Template for German cover letters (Anschreiben)
        self.german_template = """
Sehr geehrte Damen und Herren,

mit großem Interesse habe ich Ihre Stellenausschreibung für die Position als {job_title} bei {company} gelesen.
Als erfahrener {experience_field} mit {experience_years} Jahren Berufserfahrung bin ich überzeugt,
dass ich eine wertvolle Ergänzung für Ihr Team darstellen würde.

Meine Qualifikationen umfassen:
{qualifications}

Besonders interessiert mich an dieser Position {specific_interest}.
Meine Erfahrung in {relevant_skills} macht mich zu einem idealen Kandidaten für diese Rolle.

Ich freue mich darauf, Sie in einem persönlichen Gespräch von meinen Fähigkeiten zu überzeugen.

Mit freundlichen Grüßen,
{name}
"""

        # Template for English cover letters
        self.english_template = """
Dear Hiring Manager,

I am writing to express my strong interest in the {job_title} position at {company}.
With {experience_years} years of experience in {experience_field}, I am confident that I would be
a valuable addition to your team.

My key qualifications include:
{qualifications}

I am particularly drawn to this opportunity because {specific_interest}.
My background in {relevant_skills} makes me well-suited for this role.

I look forward to discussing how my skills and experience can contribute to your team's success.

Best regards,
{name}
"""

    def generate_cover_letter(self, job: JobListing, language: str = "german",
                            custom_params: Dict = None) -> str:
        """Generate a personalized cover letter"""

        # Default parameters (customize based on your background)
        params = {
            'job_title': job.title,
            'company': job.company,
            'name': self.criteria.name,
            'experience_years': '5+',  # CUSTOMIZE
            'experience_field': 'Software Development',  # CUSTOMIZE
            'qualifications': '• Expertise in Python, Java, and web technologies\n• Strong analytical and problem-solving skills\n• Experience with agile development methodologies',  # CUSTOMIZE
            'specific_interest': 'the opportunity to work with cutting-edge technologies and contribute to innovative projects',  # CUSTOMIZE
            'relevant_skills': 'software development, data analysis, and project management'  # CUSTOMIZE
        }

        # Override with custom parameters
        if custom_params:
            params.update(custom_params)

        # Select template based on language
        if language.lower() == "german":
            template = self.german_template
        else:
            template = self.english_template

        # Generate cover letter
        cover_letter = template.format(**params)

        return cover_letter.strip()

    def save_cover_letter(self, job: JobListing, language: str = "german",
                         custom_params: Dict = None) -> str:
        """Generate and save cover letter to file"""
        cover_letter = self.generate_cover_letter(job, language, custom_params)

        # Create filename
        safe_company = re.sub(r'[^\w\s-]', '', job.company).strip()
        safe_title = re.sub(r'[^\w\s-]', '', job.title).strip()
        filename = f"cover_letter_{safe_company}_{safe_title}_{language}.txt"

        # Save to file
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(cover_letter)

        logging.info(f"Cover letter saved: {filename}")
        return filename

class EmailNotifier:
    """Send email notifications for new job matches"""

    def __init__(self, smtp_server: str = "smtp.gmail.com", smtp_port: int = 587):
        self.smtp_server = smtp_server
        self.smtp_port = smtp_port

    def send_job_alert(self, jobs: List[JobListing], recipient_email: str,
                      sender_email: str, sender_password: str):
        """Send email alert with new job matches"""

        if not jobs:
            return

        # Create email content
        subject = f"New Job Matches Found - {len(jobs)} positions"

        body = f"""
Hello,

Found {len(jobs)} new job matches for your search criteria:

"""

        for i, job in enumerate(jobs[:10], 1):  # Limit to top 10
            body += f"""
{i}. {job.title} at {job.company}
   Location: {job.location}
   Match Score: {job.match_score:.1f}%
   Source: {job.source}
   URL: {job.url}

"""

        body += """
Best regards,
Austrian Job Search Bot
"""

        # Send email
        try:
            msg = MimeMultipart()
            msg['From'] = sender_email
            msg['To'] = recipient_email
            msg['Subject'] = subject

            msg.attach(MimeText(body, 'plain'))

            server = smtplib.SMTP(self.smtp_server, self.smtp_port)
            server.starttls()
            server.login(sender_email, sender_password)
            text = msg.as_string()
            server.sendmail(sender_email, recipient_email, text)
            server.quit()

            logging.info(f"Email alert sent to {recipient_email}")

        except Exception as e:
            logging.error(f"Error sending email: {e}")

def main():
    """Main function to run the job search"""

    # Initialize search criteria (CUSTOMIZE THESE VALUES)
    criteria = JobSearchCriteria(
        name="Badie Mouelhi",
        email="<EMAIL>",
        # Add your specific keywords, skills, and preferences here
        keywords=["software engineer", "developer", "python", "java", "data analyst"],
        job_titles=["Software Engineer", "Developer", "Data Analyst"],
        locations=["Wien", "Vienna", "Graz", "Salzburg", "Austria"],
        experience_level="mid"
    )

    # Initialize components
    searcher = AustrianJobSearcher(criteria)
    tracker = ApplicationTracker()
    cover_letter_gen = CoverLetterGenerator(criteria)
    notifier = EmailNotifier()

    print("🔍 Starting Austrian Job Search...")
    print(f"Search criteria: {', '.join(criteria.keywords[:3])}...")
    print(f"Target locations: {', '.join(criteria.locations[:3])}...")

    # Search for jobs
    jobs = searcher.search_all_boards()

    if not jobs:
        print("❌ No jobs found matching your criteria")
        return

    # Filter high-quality matches
    good_matches = searcher.filter_jobs(min_score=40.0)

    print(f"\n📊 Results Summary:")
    print(f"Total jobs found: {len(jobs)}")
    print(f"High-quality matches (40%+ score): {len(good_matches)}")

    # Save results
    excel_file = searcher.save_to_excel()
    json_file = searcher.save_to_json()

    print(f"\n💾 Results saved to:")
    print(f"Excel: {excel_file}")
    print(f"JSON: {json_file}")

    # Show top matches
    print(f"\n🎯 Top 5 Job Matches:")
    for i, job in enumerate(good_matches[:5], 1):
        print(f"{i}. {job.title} at {job.company}")
        print(f"   📍 {job.location} | 🎯 {job.match_score:.1f}% match | 🌐 {job.source}")
        print(f"   🔗 {job.url}")
        print()

    # Generate sample cover letter for top match
    if good_matches:
        top_job = good_matches[0]
        print(f"📝 Generating sample cover letter for: {top_job.title} at {top_job.company}")
        cover_letter_file = cover_letter_gen.save_cover_letter(top_job, "german")
        print(f"Cover letter saved: {cover_letter_file}")

    # Show application tracking summary
    app_summary = tracker.get_applications_summary()
    if app_summary:
        print(f"\n📈 Application Status Summary:")
        for status, count in app_summary.items():
            print(f"   {status}: {count}")

    print(f"\n✅ Job search completed successfully!")
    print(f"Check the generated files for detailed results.")

if __name__ == "__main__":
    main()
